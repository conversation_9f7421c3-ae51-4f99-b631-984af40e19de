<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ChatLineRound, Service,Avatar,Management, Close, ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import FullScreenNew from '@/components/FullScreenNew.jsx'
import FullScreenNo from '@/components/FullScreenNo.jsx'
import Chat from '@/components/Chat.jsx'
import { ElMessageBox } from 'element-plus'
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const currentMenu = ref('qa')
const isFullScreen = ref(false)

// 侧边栏折叠状态
const isSidebarCollapsed = ref(false)

// 计算属性判断是否显示侧边栏
const showSidebar = computed(() => {
  return userStore.isLoggedIn && route.path !== '/login'
})

const switchMenu = (menu) => {
  currentMenu.value = menu
  if (menu === 'qa') {
    router.push('/')
  } else if (menu === 'ai') {
    router.push('/ai')
  }
}
// 获取用户名
const username = computed(() => {
  return userStore.username || '未登录'
})
// 关闭iframe
const handleClose = () => {
  if (window.parent && typeof window.parent.closeMyplant === 'function') {
    window.parent.closeMyplant()
  }
}
// 处理退出登录
const handleLogout = () => {
  ElMessageBox.confirm(
    '确定要退出登录吗?',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    userStore.logout()
    router.push('/login')
  }).catch(() => {})
}
// 切换全屏
const toggleFullScreen = () => {
  isFullScreen.value = !isFullScreen.value
  const parentElement = window.parent.document.querySelector('.myplant')
  if (parentElement) {
    parentElement.style.width = isFullScreen.value ? '100%' : ''
    parentElement.style.height = isFullScreen.value ? '100%' : ''
  }
}

// 切换侧边栏折叠状态
const toggleSidebar = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value
  localStorage.setItem('layout_sidebar_collapsed', isSidebarCollapsed.value ? '1' : '0')
}

// 初始化侧边栏状态
onMounted(() => {
  const savedState = localStorage.getItem('layout_sidebar_collapsed')
  if (savedState) {
    isSidebarCollapsed.value = savedState === '1'
  }
})
</script>

<template>
  <div class="app-container">
    <div class="main-content" :class="{ 'with-sidebar': showSidebar }">
      <!-- 将侧边栏移到左侧 -->
      <div class="sidebar siderbar-menu">
        <div class="logo">
        <img src="@/assets/images/bot-avatarnew.png" alt="Logo" />
        <!-- <h1>合同预审AI智能体平台</h1> -->
      </div>
        <!-- <div class="sidebar-header">
          <el-button
            class="close-btn"
            @click="handleClose"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div> -->
        <div class="menu-item" :class="{ 'active': currentMenu === 'qa' }" @click="switchMenu('qa')">
          <el-icon><Chat /></el-icon>
          <span>对话</span>
        </div>
        <!-- <div class="menu-item" :class="{ 'active': currentMenu === 'ai' }" @click="switchMenu('ai')">
          <el-icon><Management /></el-icon>
          <span>智能体</span>
        </div> -->
        <!-- 用户信息区域 - 移动到侧边栏底部 -->
      <div class="sidebar-footer">
        <div class="user-info">
          <el-avatar :size="24" icon="Avatar" />
          <span>{{ username }}</span>
        </div>
        <div class="action-btn" @click="handleLogout">
          <el-icon class="action-icon"><SwitchButton /></el-icon>
          <!-- <span class="action-text">退出</span> -->
        </div>
      </div>
      </div>

      <div class="qa-container" :class="{ 'sidebar-collapsed': isSidebarCollapsed }">
        <router-view />
      </div>
    </div>
  </div>
</template>

<style>
@import '@/assets/styles/main.css';

/* 侧边栏底部用户信息样式 */
.action-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 5px;
  cursor: pointer;
}
.sidebar-footer {
  margin-top: auto;
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  flex-direction: column;
  font-size: 16px; /* 与导航栏文字大小保持一致 */
}
.sidebar-footer .action-icon, 
.sidebar-footer .el-avatar {
  width: 45px; /* 与导航栏图标大小保持一致 */
  height: 45px; /* 与导航栏图标大小保持一致 */
  font-size: 26px;
}
.sidebar-footer .el-avatar{
  background-color: #4a90e5;
  color: #fff;
  border:2px solid #fff;
}
.user-info{
  text-align: center;
}

.logout-btn {
  color: #fff;
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar.siderbar-menu {
  background: #4a90e5;
  border:0px none;
  color: #fff;
  max-width: 70px;
  margin-right: 10px;
  box-shadow:none;
}

/* 自定义Element Plus组件样式 */
.el-button.fullscreen-btn {
  padding: var(--spacing-sm);
  font-size: 22px;
  color: var(--color-text-secondary);
  background: transparent;
  border: 0px none;
}

.el-button.fullscreen-btn:hover {
  color: var(--color-primary);
}

.el-button.close-btn {
  padding: var(--spacing-sm);
  font-size: 20px;
  border-radius: var(--border-radius-circle);
  width: 38px;
  height: 38px;
  color: var(--color-error);
  background-color: var(--color-background);
  border: 0px none;
}

.el-button.close-btn:hover {
  color: var(--color-error);
  background-color: #efefef;
}

/* Logo样式 */
.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 0 15px;
}

.logo img {
  width: 50px;
  /* height: 60px; */
  border-radius: var(--border-radius-circle);
}
</style>

