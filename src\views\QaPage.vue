<template>
  <div class="qa-page">
    <!-- 添加历史会话侧边栏 -->
    <div class="sidebar" :class="{ 'collapsed': isSidebarCollapsed }">
      <div class="sidebar-header search-flex-wrap" :class="{ 'search-focus': isSearchFocused || searchQuery }">
        <button
          class="new-chat-btn-gradient flex-anim-btn"
          @click="startNewChat"
        >
          <el-icon><Plus /></el-icon>
          <span>新建对话</span>
        </button>
        <div
          class="search-input-gradient flex-anim-search"
          @click="handleSearchIconClick"
          :style="{ cursor: isSearchFocused ? 'text' : 'pointer' }"
        >
          <el-icon class="search-input-icon"><Search /></el-icon>
          <input
            ref="searchInputRef"
            v-model="searchQuery"
            class="search-input-real"
            placeholder="搜索历史记录"
            @focus="handleSearchIconClick"
            @blur="onSearchBlur"
            @input="handleSearch"
            :tabindex="isSearchFocused ? 0 : -1"
            :style="{ pointerEvents: isSearchFocused ? 'auto' : 'none' }"
          />
          <el-icon
            v-if="searchQuery"
            class="search-clear-icon"
            @mousedown.prevent="clearSearch"
          ><CircleClose /></el-icon>
        </div>
      </div>
      
      <div class="sidebar-content">
        <conversation-list 
          :active-conversation="conversationId"
          @select-conversation="handleSelectConversation" 
        />
      </div>
    </div>    
    <!-- 主要内容区域 -->
    <!-- <div class="main-content qa-page-main-content" :class="{ 'expanded': isSidebarCollapsed }"> -->
    <div class="main-content expanded">
      <!--<div class="header">
        <div class="title">Hi~我是豫小纪</div>          
        <div class="logo">你身边的领导话语材料智能助手，可以为你深度检索材料、答疑解惑，快来体验吧~~</div>
         <div class="header-actions">
          <button class="new-chat-btn" @click="startNewChat">
              <el-icon><Plus /></el-icon> 
              <span>新对话</span>
          </button>
        </div>
      </div> -->
      
      <div class="chat-container" ref="chatContainerRef">
        <!-- 加载更多历史消息按钮 -->
        <load-more-messages v-if="messages.length > 0 && hasMoreMessages" />
        
        <!-- 滚动到底部按钮 -->
        <div v-if="showScrollButton" class="scroll-to-bottom" @click="scrollToBottomManually">
          <!-- <el-tooltip content="滚动到最新消息" placement="top"> -->
              <el-icon><ArrowDown /></el-icon>
          <!-- </el-tooltip> -->
        </div>
        
        <!-- 初始推荐卡片 -->
        <div v-if="messages.length === 0" class="recommendation-cards">
          <div class="welcome-message">
            <div class="message-item message-assistant">
              <div class="message-avatar message-avatar-assistant">
                <img :src="botAvatar" alt="avatar" />
              </div>
              <div class="message-content" style="max-width: 100%; padding-bottom: 0px;padding-right:0px;">
                <div class="message-text assistant-text" v-html="marked(welcomeMessage)">                
                </div>
              </div>
            </div>
          </div>
          <div class="recommendation-cards-list">
            <div 
              v-for="(card, index) in recommendCards" 
              :key="index"
              class="recommendation-card"
              @click="handleCardClick(card)"
            >
              {{ card }}
            </div>
          </div>
          <div class="guide-special-note">
            <strong>特别提醒：</strong>问答由 AI 生成，内容仅供参考，具体以所在单位上级组织意见为准。
          </div>        
        </div>
        
        <!-- 聊天消息 -->
        <div v-else class="message-list">
          <div v-for="(message, index) in messages" 
            :key="index"
            :class="['message-item', message.role === 'user' ? 'message-user' : 'message-assistant']"
          >
            <div class="message-avatar">
              <img :src="message.role === 'user' ? userAvatar : botAvatar" alt="avatar" />
            </div>
            <div class="message-content"
             :style="message.role !== 'user' && (suggestedQuestions.length > 0 && !loading && !isResponseStopped)?'padding-bottom: 0px;':''">
              <div v-if="message.role === 'user'" class="message-text user-text">
                <div>{{ message.content }}</div>
                <!-- 显示附件 - 包括files和message_files字段 -->
                <div v-if="(message.files && message.files.length > 0)" class="message-files">
                  <!-- 处理新发送消息的附件 -->
                  <div v-for="(file, index) in message.files || []" :key="`file-${index}`" class="message-files-item">
                    <el-icon><Paperclip /></el-icon>
                    <!-- <span>{{ file.name }}</span> -->
                    <span @click="openFile(file)">{{ file.name }}</span>
                  </div>   
                </div>
                <!-- 用户消息反馈按钮，初始隐藏，hover显示 -->
                <div class="user-feedback-buttons">
                  <button class="feedback-btn copy" @click="copyMessage(message.content)">
                    <el-tooltip content="复制" placement="top">
                      <el-icon><DocumentCopy /></el-icon>
                    </el-tooltip>
                  </button>
                </div>
              </div>
              <div v-else class="message-text assistant-text">
                <!-- 加载中状态 -->
                <div v-if="loading && !message.content && index===messages.length-1" class="loading-indicator">
                  <el-icon class="loading-icon" :size="20"><Loading /></el-icon>
                  <span>Thinking...</span>
                </div>
                <!-- <div class="message-text-html" v-html="marked(message.content)"></div> -->
                <div class="message-text-html" v-html="marked(formatMessage(message.content))"></div>
                
                <!-- 反馈按钮 - 历史消息始终显示，当前消息在流式输出完成后显示 -->
                <div v-if="((message.content && message.id !== lastMessageId && message.isLs===undefined) || (message.id === lastMessageId && !loading))" class="feedback-buttons">
                  <button class="feedback-btn copy" @click="copyMessage(message.content)">
                    <el-tooltip content="复制" placement="top">
                      <el-icon><DocumentCopy /></el-icon>
                    </el-tooltip>
                  </button>
                  <button v-if="message.content !== '已暂停生成。'" class="feedback-btn like" 
                    :class="{ 'active': message.feedback && message.feedback.rating === 'like' }"
                    @click="submitFeedback(message, 'like')">
                    <el-tooltip content="点赞" placement="top">
                      <ThumbUp />
                    </el-tooltip>
                  </button>
                  <button v-if="message.content !== '已暂停生成。'" class="feedback-btn dislike"
                    :class="{ 'active': message.feedback && message.feedback.rating === 'dislike' }"
                    @click="submitFeedback(message, 'dislike')">
                    <el-tooltip content="点踩" placement="top">
                      <ThumbDown />
                    </el-tooltip>
                  </button>
                  <button v-if="message.content !== '已暂停生成。'" class="feedback-btn export"
                    @click="exportToWord(message,index)">
                    <el-tooltip content="导出为Word文档" placement="top">
                      <el-icon size="18"><Download /></el-icon>
                    </el-tooltip>
                  </button>
                </div>
              </div>  
            </div>
          </div>
          
          <!-- 建议问题列表 -->
          <div v-if="suggestedQuestions.length > 0 && !loading && !isResponseStopped && shouldShowSuggestions" class="suggested-questions">
            <div class="suggested-title">你可能想问：</div>
            <div class="suggested-list">
              <div 
                v-for="(question, index) in suggestedQuestions" 
                :key="index"
                class="suggested-question"
                @click="handleCardClick(question)"
              >
                {{ question }}
              </div>
            </div>
          </div>
        </div>     
        
      </div>
      
      <!-- 输入框区域 -->
      <div class="input-area" :style="{ 'padding-top': fileNames.length > 0 ? '5px' : '20px' }">
        <!-- 添加统计信息组件 -->
        <statistics-bar class="statistics-section" />
        <div class="input-container">
          <!-- 文件上传预览区 -->
          <div v-if="fileNames.length > 0" class="file-preview">
            <div v-for="(fileName, index) in fileNames" :key="index" class="file-item">
              <span class="file-name">{{ fileName }}</span>
              <el-icon class="remove-file" @click="removeFile(index)"><Close /></el-icon>
            </div>
          </div>
          
          <!-- 输入框 -->
          <div class="input-wrapper">
            <textarea
              v-model="inputText"
              placeholder="我在，有什么想咨询的都可以对我说哦~"
              class="input-textarea"
              @keydown.enter.exact.prevent="handleSend"
            ></textarea>
            
            <!-- 文件上传按钮 -->
            <div class="input-tools">
              <div class="upload-btn" @click="triggerFileUpload">
                <el-tooltip content="上传附件（仅识别文字）<br/><span style='font-size: 12px;color: #999;'>支持上传docx、pptx、pdf的文件，文件大小不超过30KB</span>" raw-content placement="top">
                  <el-icon><FileUpload /></el-icon>
                </el-tooltip>
              </div>
              <input
                ref="fileInputRef"
                type="file"
                accept=".docx,.pptx,.pdf"
                class="file-input"
                @change="handleFileSelect"
              />
            </div>
            
            <!-- 发送/停止按钮 - 修改为根据loading状态切换 -->
            <div 
              :class="loading ? 'send-btn stop-btn-small' : 'send-btn'" 
              @click="loading ? stopGeneration() : handleSend()"
            >
              <el-icon v-if="loading"><CircleClose /></el-icon>
              <el-icon v-else><Position /></el-icon>
            </div>
          </div>
        </div>
      </div>    
      <!-- 反馈详情弹窗 -->
      <el-dialog
        v-model="feedbackDialogVisible"
        title="提交反馈"
        width="40%"
        :show-close="true"
      >
        <el-form>
          <el-form-item>
            <el-input
              v-model="feedbackContent"
              type="textarea"
              :rows="4"
              placeholder="请告诉我们您的反馈意见（选填）"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitDetailedFeedback">提交</el-button>
            <el-button @click="feedbackDialogVisible = false">取消</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch, onUnmounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useConversationStore } from '../stores/conversation'
import { difyApi } from '../api/dify'
import { useFileUpload } from '../composables/useFileUpload'
import { v4 as uuidv4 } from 'uuid'
import { 
  Loading, Position, Paperclip, Close, Document, Upload, CircleClose, DocumentCopy, Plus, ChatDotRound, ChatLineRound, ArrowLeft, ArrowRight, ArrowDown, Search
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { marked } from 'marked'
import ThumbDown from '../components/ThumbDown.jsx'
import ThumbUp from '../components/ThumbUp.jsx'
import FileUpload from '../components/FileUpload.jsx'
import StatisticsBar from '../components/StatisticsBar.vue';
import ConversationList from '../components/ConversationList.vue'
import ConversationSearch from '../components/ConversationSearch.vue'
import LoadMoreMessages from '../components/LoadMoreMessages.vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { encrypt } from '@/utils/rsa'
import axios from 'axios'


// 配置marked选项
marked.use({
  breaks: true,
  gfm: true,
  headerIds: false,
  mangle: false,
  hooks: {
    preprocess: (markdown) => 
      markdown.replace(/<think>/g, '<div class="thinking">').replace(/<\/think>/g, '</div>')
      // markdown.replace(/<\/?details>/g, '').replace(/<\/?think>/g, '')    // 过滤 think 标签
  }
})
// 用户头像和机器人头像
import botAvatar from '@/assets/images/bot-avatar.png'
import userAvatar from '@/assets/images/user-avatar.png'

// 获取状态管理
const conversationStore = useConversationStore()
const { 
  messages, 
  loading, 
  conversationId, 
  taskId: currentTaskId, 
  lastMessageId,
  hasMoreMessages,
  suggestedQuestions,
  isResponseStopped,
  isLoadingHistory
} = storeToRefs(conversationStore)

const { 
  addMessage, 
  setLoading, 
  updateMessageContent, 
  addCitation, 
  setConversationId,
  setTaskId,
  setLastMessageId,
  setSuggestedQuestions,
  stopResponse
} = conversationStore

// 文档预览相关状态
const documentPreviewVisible = ref(false)
const previewLoading = ref(false)
const previewUrl = ref('')
const previewTitle = ref('')

// 反馈相关状态
const feedbackDialogVisible = ref(false)
const feedbackContent = ref('')
const currentFeedbackMessageId = ref(null)
const currentFeedbackIsPositive = ref(true)

// 输入相关状态
const inputText = ref('')
const chatContainerRef = ref(null)

const userStore = useUserStore()

// 文本内容
const welcomeMessage = '我是豫小纪！你身边的纪法知识问答智能助手，可以为你深度检索材料、答疑解惑，助你时刻绷紧纪律之弦，守好底线，不越红线，筑牢防线。可以这样问：'

// 自动滚动控制变量
const shouldAutoScroll = ref(true)
const userScrolled = ref(false)

// 是否显示滚动到底部按钮的计算属性
const showScrollButton = computed(() => {
  if (!chatContainerRef.value) return false
  
  // 如果用户已经滚动过，且当前不在底部，则显示滚动按钮
  const container = chatContainerRef.value
  const notAtBottom = container.scrollHeight - container.scrollTop - container.clientHeight > 50
  
  return userScrolled.value && notAtBottom
})

// 是否显示建议问题的计算属性
const shouldShowSuggestions = computed(() => {
  // 确保只有当前会话的建议才显示
  return conversationId.value && suggestedQuestions.value.length > 0
})

// 检查是否在底部的函数
const isAtBottom = () => {
  if (!chatContainerRef.value) return true
  const container = chatContainerRef.value
  // 容差值20px，避免滚动条接近底部但不完全到底时的误判
  return container.scrollHeight - container.scrollTop - container.clientHeight < 50
}

// 处理滚动事件
const handleScroll = () => {
  if (!chatContainerRef.value) return
  
  // 判断是否滚动到底部
  const atBottom = isAtBottom()
  
  // 如果用户滚动且不在底部，取消自动滚动
  if (!atBottom && shouldAutoScroll.value) {
    shouldAutoScroll.value = false
    userScrolled.value = true
  } else if (!atBottom && !userScrolled.value) {
    // 即使不是自动滚动状态，只要不在底部就标记为用户已滚动
    userScrolled.value = true
  }
  
  // 如果用户手动滚动到底部，且流式加载未结束，恢复自动滚动
  if (atBottom && loading.value && userScrolled.value) {
    shouldAutoScroll.value = true
    userScrolled.value = false
  } else if (atBottom && userScrolled.value) {
    // 非加载状态下，滚动到底部时也重置用户滚动标志
    userScrolled.value = false
  }
}

// 获取引用标题，处理长标题
const getCitationTitle = (citation, showFull = false, isFloating = false) => {
  if (!citation || !citation.title) return '未知文档';
  
  // 获取文档名称
  let docName = citation.title;
  
  // 从引用的文本内容解析文档名称和原文
  if (citation.text && citation.text.includes('<文档名称>')) {
    const nameMatch = citation.text.match(/<文档名称>([\s\S]*?)(?:<文档链接>|<引用原文>)/);
    if (nameMatch && nameMatch[1]) {
      docName = nameMatch[1].trim();
    }
  }
  
  // 如果是浮动引用，或请求显示完整标题，则直接返回
  if (isFloating) {
    return docName.replace(/^《|》$/g, '').split('_')[0]; // 提取更简洁的标题用于悬浮显示
  }
  
  if (showFull) {
    return docName;
  }
  
  // 否则截断并添加省略号
  return docName.length <= 30 ? docName : docName.substring(0, 30) + '...';
}

// 推荐问题
const recommendCards = ref([  
  '《中国共产党纪律处分条例》对违规收送礼品礼金有何具体规定？',
  '如何认定送礼方属于"管理和服务对象"？',
  //'收受管理服务对象赠送的家乡土特产是否违反纪律？',
  '如何区分"正常礼尚往来"和违规收送礼品礼金？',
  '什么是"利用职权或者职务上的影响"操办婚丧喜庆事宜？',
  '如何把握党员干部点抢微信群电子红包的行为？',
  //'培训期间能否组织参观考察?',
  '在向上级汇报工作时，只汇报容易解决的问题而隐瞒困难，是否违规？',
  '领导干部收受讲课费是否需要报备？如何判断标准是否合理？',
  '"新官"对前任遗留问题能否以"非自己责任"为由拒绝处理？',
 // '如何避免政务服务热线、网站等"僵尸化"问题？',
 // '何为"可能影响公正执行公务"的情况？如何判断？',
  // '在工作中发现同事弄虚作假、隐瞒实情，应该如何处理？',
  // '出差期间能否回家省亲办事?',
  // '因隐瞒问题导致重大损失会被如何追责？'
])

// 使用文件上传组合式API
const { uploadedFiles, fileNames, handleFileSelect, removeFile, clearFiles,clearFilesN } = useFileUpload()
const fileInputRef = ref(null)

// 触发文件上传点击
const triggerFileUpload = () => {
  fileInputRef.value.click()
}

// 处理推荐卡片点击
const handleCardClick = (question) => {
  // 检查是否有问答正在进行中
  if (loading.value) {
    ElMessage.warning('正在生成回答，请等待完成后再提问')
    return
  }

  // 移除可能的箭头符号
  const cleanQuestion = question.replace(' →', '')
  inputText.value = cleanQuestion
  handleSend()
}

// 显示引用详情
const showCitationDetail = (citation) => {
  // 解析引用内容
  if (citation.text && citation.text.includes('<参考文档>')) {
    // 提取文档名称
    const nameMatch = citation.text.match(/<文档名称>([\s\S]*?)(?:<文档链接>|<引用原文>)/);
    if (nameMatch && nameMatch[1]) {
      citation.parsedTitle = nameMatch[1].trim();
    }
    
    // 提取引用原文
    const contentMatch = citation.text.match(/<引用原文>([\s\S]*?)(?=<参考文档>|$)/);
    if (contentMatch && contentMatch[1]) {
      citation.parsedContent = contentMatch[1].trim();
    }
  }
  
  // currentCitation.value = citation;
  // citationDialogVisible.value = true;
}

// 预览文档
const previewDocument = async (citation) => {
  if (!citation.document_id) {
    ElMessage.warning('没有可用的文档预览')
    return
  }
  
  // 获取文档链接
  let docLink = null;
  if (citation.text && citation.text.includes('<文档链接>')) {
    const linkMatch = citation.text.match(/<文档链接>([\s\S]*?)(?:<引用原文>|$)/);
    if (linkMatch && linkMatch[1] && linkMatch[1].trim().startsWith('http')) {
      docLink = linkMatch[1].trim();
    }
  }
  
  try {
    previewLoading.value = true;
    
    // 获取文档名称
    let docName = citation.title;
    if (citation.text && citation.text.includes('<文档名称>')) {
      const nameMatch = citation.text.match(/<文档名称>([\s\S]*?)(?:<文档链接>|<引用原文>)/);
      if (nameMatch && nameMatch[1]) {
        docName = nameMatch[1].trim();
      }
    }
    
    previewTitle.value = docName || '文档预览';
    
    // 如果有直接链接，优先使用
    if (docLink) {
      previewUrl.value = docLink;
      documentPreviewVisible.value = true;
    } else {
      // 否则通过API获取预览
      const response = await difyApi.previewDocument(citation.document_id);
      const blob = new Blob([response], { type: 'application/pdf' });
      previewUrl.value = URL.createObjectURL(blob);
      documentPreviewVisible.value = true;
    }
  } catch (error) {
    console.error('Error previewing document:', error)
    ElMessage.error('文档预览失败')
  } finally {
    previewLoading.value = false
  }
}

// 提交简单反馈
const submitFeedback = (message, isPositive=null) => {
  currentFeedbackMessageId.value = message.id
  currentFeedbackIsPositive.value = isPositive
  if(message.feedback && message.feedback.rating && isPositive==message.feedback.rating){
    currentFeedbackIsPositive.value = null
    submitDetailedFeedback();
    return
  }
  if (isPositive=='like') {
    // 如果是点赞，直接反馈
    // feedbackDialogVisible.value = true
    submitDetailedFeedback();
  } else {
    // 如果是点踩，弹出对话框收集详细反馈
    feedbackDialogVisible.value = true
  }
}

// 提交详细反馈
const submitDetailedFeedback = async () => {
  try {
    await difyApi.submitFeedback(
      currentFeedbackMessageId.value,
      currentFeedbackIsPositive.value,
      feedbackContent.value
    )
    
    // 更新消息的feedback属性
    const message = messages.value.find(msg => 
      (msg.role=="assistant" && (msg.messageId === currentFeedbackMessageId.value || msg.id === currentFeedbackMessageId.value))
    )
    if (message) {
      message.feedback = {
        rating: currentFeedbackIsPositive.value
      }
    }    
    if(currentFeedbackIsPositive.value){
      ElMessage.success('感谢您的反馈！')
    }
    
    // 清空反馈内容并关闭对话框
    feedbackContent.value = ''
    feedbackDialogVisible.value = false
  } catch (error) {
    console.error('提交反馈失败:', error)
    ElMessage.error('提交反馈失败，请重试')
  }
}

// 停止生成响应
const stopGeneration = async () => {
  try {
    // 先设置UI状态，确保即时响应
    setLoading(false)
    
    // 停止后不再获取建议问题
    setSuggestedQuestions([])
    console.log('stopGeneration', currentTaskId.value);
    // 调用API停止生成
    if (currentTaskId.value) {
      await difyApi.stopResponseStream(currentTaskId.value)
      ElMessage.info('已停止响应生成')
      
      // 设置响应已停止状态
      conversationStore.setResponseStopped(true)
    }
  } catch (error) {
    console.error('停止生成失败:', error)
    ElMessage.error('停止生成失败')
  }
}

// 格式化消息内容
const formatMessage =  (content) => {
  if (!content) return ''
        console.log(userStore,'用户信息')
  // 将换行符转换为HTML换行
  // content = content.replace(/\n\n/g, '\n')
  // content = content.replace(/\n/g, '<br>')
  
  // 存储提取的所有参考文档块
  const referenceBlocks = [];
  let processedContent = content;
  
  // 提取所有参考文档块
  let refMatch;
  let refMatch2;

  const refRegex = /<参考文档>([\s\S]*?)<\/参考文档>/g;
  const refRegex2 = /<需求链接>([\s\S]*?)<\/需求链接>/g;
  
  while ((refMatch2 = refRegex2.exec(content)) !== null) {
    const fullMatch2 = refMatch2[0];
    // 在链接后边拼接参数
    const linkMatch = fullMatch2.match(/<需求链接>([\s\S]*?)<\/需求链接>/);
    if (linkMatch && linkMatch[1]) {
      const link = linkMatch[1].trim();
      const modifiedLink = `${link}&uid=${encrypt(userStore.userInfo?.username)}`;
      // 创建a标签，使用"点击跳转详情"作为显示文字
      const linkHtml = `<a href="${modifiedLink}" target="_blank" class="requirement-link">点击跳转详情</a>`;
      processedContent = processedContent.replace(fullMatch2, linkHtml);
    }
  }
   



  
  while ((refMatch = refRegex.exec(content)) !== null) {
    const fullMatch = refMatch[0];
    const docName = fullMatch.match(/<文档名称>([\s\S]*?)<\/文档名称>/)?.[1]?.trim() || '';
    const docLink = fullMatch.match(/<文档链接>([\s\S]*?)<\/文档链接>/)?.[1]?.trim() || '';
    const docContent = fullMatch.match(/<引用原文>([\s\S]*?)<\/引用原文>/)?.[1]?.trim() || '';
    
    
    referenceBlocks.push({
      fullMatch,
      docName,
      docLink,
      docContent,
      xuqiuContent,
      index: refMatch.index
    });
  }
  
  // 按文档名分组并按索引排序
  const groupedBlocks = {};
  referenceBlocks.forEach(block => {
    if (!groupedBlocks[block.docName]) {
      groupedBlocks[block.docName] = [];
    }
    groupedBlocks[block.docName].push(block);
  });
  
  // 对每个文档名，合并相邻的引用块
  for (const docName in groupedBlocks) {
    // 按索引排序
    const blocks = groupedBlocks[docName].sort((a, b) => a.index - b.index);
    
    // 检查哪些块是相邻的并合并它们
    let i = 0;
    while (i < blocks.length) {
      const currentBlock = blocks[i];
      const mergedContents = [currentBlock.docContent];
      let j = i + 1;
      
      // 找出所有相邻的块
      while (j < blocks.length) {
        const nextBlock = blocks[j];
        // 检查两个块在原始内容中是否紧邻
        const currentBlockEnd = currentBlock.index + currentBlock.fullMatch.length;
        const textBetween = content.substring(currentBlockEnd, nextBlock.index).replace(/<br>/g, '').trim();
        
        // 如果两个块之间没有实质内容，视为相邻
        if (textBetween === '') {
          mergedContents.push(nextBlock.docContent);
          // 标记这个块已被合并
          blocks[j].merged = true;
          j++;
        } else {
          break;
        }
      }
      // 如果有需要合并的块
      
      if (mergedContents.length > 1) {
        // 清理链接中的<br>标签，处理特殊格式的文档链接
        const cleanLink = currentBlock.docLink.replace(/<br>\s*/g, '');
        // 为所有文档链接创建特殊的点击处理元素
        const linkHtml = `<a href="javascript:void(0)" class="doc-preview-link" 
                            data-doc-url="${encodeURIComponent(cleanLink)}" 
                            data-doc-content="${encodeURIComponent(mergedContents[0])}">${currentBlock.docName}</a>`;
        
        const mergedHtml = `<div class="reference-block">
          <div class="reference-doc-name">
            ${linkHtml}
          </div>
          ${mergedContents.map(content => `<div class="reference-content">${content}</div>`).join('')}
        </div>`;
        
        // 替换第一个块
        processedContent = processedContent.replace(currentBlock.fullMatch, mergedHtml);
        
        // 删除其他已合并的块
        for (let k = i + 1; k < j; k++) {
          processedContent = processedContent.replace(blocks[k].fullMatch, '');
        }
        
        i = j;
      } else {
        // 没有相邻块需要合并，按原样处理
        // 清理链接中的<br>标签，处理特殊格式的文档链接
        const cleanLink = currentBlock.docLink.replace(/<br>\s*/g, '');
        
        // 为所有文档链接创建特殊的点击处理元素
        const linkHtml = `<a href="javascript:void(0)" class="doc-preview-link" 
                            data-doc-url="${encodeURIComponent(cleanLink)}" 
                            data-doc-content="${encodeURIComponent(currentBlock.docContent)}">${currentBlock.docName}</a>`;
        
        const singleHtml = `<div class="reference-block">
          <div class="reference-doc-name">
            ${linkHtml}
          </div>
          <div class="reference-content">${currentBlock.docContent}</div>
        </div>`;
        
        // 仅替换这一个块
        if (!currentBlock.merged) {
          processedContent = processedContent.replace(currentBlock.fullMatch, singleHtml);
        }
        
        i++;
      }
    }
  }
  
  // 去掉reference-block之间的换行
  processedContent = processedContent.replace(/<\/div>\s*<br>\s*<div class="reference-block">/g, '</div><div class="reference-block">');
  
  return processedContent;
}

// 手动滚动到底部（用于点击按钮）
const scrollToBottomManually = () => {
  if (chatContainerRef.value) {
    // 恢复自动滚动状态
    shouldAutoScroll.value = true
    userScrolled.value = false
    
    // 立即滚动到底部
    chatContainerRef.value.scrollTo({
      top: chatContainerRef.value.scrollHeight,
      behavior: 'smooth'
    })
  }
}

// 发送消息处理
const handleSend = async () => {
  // 检查是否有问答正在进行中
  if (loading.value) {
    ElMessage.warning('正在生成回答，请等待完成后再发送新问题')
    return
  }

  if (!inputText.value.trim()) {
    ElMessage.warning('请输入想咨询的问题')
    return
  }
  
  // 重置自动滚动状态，确保新对话开始时自动滚动
  shouldAutoScroll.value = true
  userScrolled.value = false
  
  // 准备用户消息内容，加入文件信息
  let userContent = inputText.value;
  if (uploadedFiles.value.length > 0) {
    // 添加附件信息到消息内容
    const filesList = fileNames.value.map(name => `- ${name}`).join('\n');
    // userContent += userContent ? '<br>附件：' + filesList : '附件：' + filesList;
  }
  
  const userMessageId = uuidv4();
  const userMessage = {
    id: userMessageId,
    role: 'user',
    content: userContent,
    timestamp: new Date().toISOString(),
    files: uploadedFiles.value.map(file => ({
      name: file.upload_file_name,
      id: file.upload_file_id
    }))
  }
  
  // 添加用户消息
  addMessage(userMessage)
  
  const assistantMessageId = uuidv4();
  // 创建助手消息
  const assistantMessage = {
    id: assistantMessageId,
    role: 'assistant',
    content: '',
    isLs: true,
    timestamp: new Date().toISOString(),
    conversationId: null,
    taskId: null,
    messageId: null,
    feedback: null
  }
  
  // 添加助手消息
  addMessage(assistantMessage)
  
  // 清空建议问题
  setSuggestedQuestions([])
  
  // 设置加载状态
  setLoading(true)
  
  // 保存用户输入，然后清空
  const query = inputText.value
  inputText.value = ''
  
  try {
    // 滚动到底部
    await scrollToBottom()
    
    // 获取当前会话ID
    const currentConversationId = conversationId.value
    clearFilesN();
    // 流式获取回答
    const result = await difyApi.streamChat(
      query,
      // 处理文本块
      (chunk) => {
        // 更新助手消息内容
        updateMessageContent(assistantMessage.id, (assistantMessage.content || '') + chunk)
               
        // 异步滚动到底部
        nextTick(() => scrollToBottom())
      },
      // 处理引用
      (citation) => {
        // 添加引用
        addCitation(assistantMessage.id, citation)
      },
      // 处理建议问题
      (questions) => {
        setSuggestedQuestions(questions)
        nextTick(() => scrollToBottom())
      },
      uploadedFiles.value,
      currentConversationId,
      (messageId) => {
        //更新message的id
        messages.value.forEach(message => {
          if(message.id == assistantMessageId){
            message.id = messageId
            delete message.isLs
            console.log('messageId',messages)
          }
        })        
        nextTick(() => scrollToBottom())
      }
    )
  } catch (error) {
    console.error('Error sending message:', error)
    ElMessage.error('发送消息失败，请重试')
  } finally {
    setLoading(false)
    clearFiles(); // 清空已上传文件
    clearFilesN();
  }
}

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick()
  if (chatContainerRef.value && shouldAutoScroll.value) {
    // 使用平滑滚动效果
    chatContainerRef.value.scrollTo({
      top: chatContainerRef.value.scrollHeight,
      behavior: 'smooth'
    })
  }
}

// 监听消息变化，滚动到底部
watch(() => messages.value.length, (newLength, oldLength) => {
  // 只有在以下情况才滚动到底部：
  // 1. 新消息数量大于旧消息数量(消息增加)
  // 2. 不是在加载历史消息
  if (newLength > oldLength && !isLoadingHistory.value) {
    nextTick(() => {
      scrollToBottom();
    });
  }
});
//监听下一轮建议问题列表
watch(suggestedQuestions, (newQuestions, oldQuestions) => {
  console.log('watch-suggestedQuestions',newQuestions,oldQuestions)
  if (newQuestions.length > oldQuestions.length) {
    nextTick(() => {
      scrollToBottom();
    });
  }
});

// 清理预览URL
watch(documentPreviewVisible, (visible) => {
  if (!visible && previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
    previewUrl.value = ''
  }
})

const copyMessage = async (content) => {
    try {
      // 去除HTML标签，只复制纯文本
      const plainText = content.replace(/<[^>]*>/g, '');

      // 使用现代的Clipboard API
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(plainText);
      } else {
        // 兼容性处理，使用传统方法
        const textarea = document.createElement('textarea');
        textarea.value = plainText;
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand('copy');
        document.body.removeChild(textarea);
      }

      // 提示用户复制成功
      ElMessage({
        message: '复制成功',
        type: 'success',
        duration: 1500
      });
    } catch (error) {
      console.error('复制失败:', error);
      ElMessage({
        message: '复制失败，请重试',
        type: 'error',
        duration: 1500
      });
    }
  }

// 侧边栏折叠状态
const isSidebarCollapsed = ref(false)

// 路由对象
const route = useRoute()
const router = useRouter()

// 切换侧边栏显示/隐藏
const toggleSidebar = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value
  localStorage.setItem('qa_sidebar_collapsed', isSidebarCollapsed.value ? '1' : '0')
}

// 处理选择会话
const handleSelectConversation = async (id) => {
  if (!id) {
    // 如果没有ID，表示创建新会话
    startNewChat()
    return
  }
  // 如果正在加载响应，阻止切换会话
  if (loading.value) {
    ElMessage.warning('正在生成回答，请等待完成后再切换会话')
    return
  }
  // 如果切换的会话ID与当前会话ID相同，则不进行切换
  if(id==route.params.conversationId && messages.length>0){
    return;
  }
  
  setSuggestedQuestions([]);
  try {
    // 加载会话消息
    await conversationStore.loadConversationMessages(id)
    
    // 更新URL
    router.push(`/qa/${id}`)
  } catch (error) {
    console.error('加载会话失败:', error)
    ElMessage.error('加载会话失败，请重试')
  }
}

// 初始化时检查URL参数
onMounted(async () => {
  // 恢复侧边栏状态
  const savedState = localStorage.getItem('qa_sidebar_collapsed')
  if (savedState) {
    isSidebarCollapsed.value = savedState === '1'
  }
  
  // 检查URL中是否有会话ID
  const { conversationId: routeConversationId } = route.params
  if (routeConversationId) {
    handleSelectConversation(routeConversationId)
  }

  // 添加滚动事件监听
  if (chatContainerRef.value) {
    chatContainerRef.value.addEventListener('scroll', handleScroll)
  }
  
  // 加载推荐问题
  try {
    // 实际项目中可以从API获取推荐问题
    // const recommendations = await difyApi.getRecommendations()
    // recommendCards.value = recommendations.data || recommendCards.value
  } catch (error) {
    console.error('Failed to load recommendations:', error)
  }
  
  // 添加文档链接点击事件处理
  document.addEventListener('click', (event) => {
    // 检查是否点击了文档预览链接
    if (event.target.closest('.doc-preview-link')) {
      const link = event.target.closest('.doc-preview-link')
      const docUrl = decodeURIComponent(link.dataset.docUrl)
      const docContent = decodeURIComponent(link.dataset.docContent)
      
      // 调用处理函数
      handleDocumentPreview(docUrl, docContent.substring(0, 100))
      
      // 阻止默认事件
      event.preventDefault()
    }
  })
})

// 确保在组件卸载时移除事件监听
onUnmounted(() => {
  // 移除滚动事件监听
  if (chatContainerRef.value) {
    chatContainerRef.value.removeEventListener('scroll', handleScroll)
  }

  // 移除文档点击事件
  document.removeEventListener('click', (event) => {
    if (event.target.closest('.doc-preview-link')) {
      event.preventDefault()
    }
  })
})

// 监听会话ID的变化
watch(() => route.params.conversationId, (newId) => {
  if (newId && newId !== conversationId.value) {
    handleSelectConversation(newId)
  }
})

// 新建对话函数
const startNewChat = async () => {
  // 如果正在生成回答，先停止生成
  if (loading.value) {
    try {
      // 停止任何正在进行的流式输出
      if (currentTaskId.value) {
        await stopGeneration()
      }
    } catch (error) {
      console.error('停止生成失败:', error)
      // 即使停止失败也继续创建新会话
    }
  }

  ElMessage.success('已创建新对话')
  
  // 明确清空会话ID
  setConversationId(null,"startNewChat")
  setTaskId(null)
  setLastMessageId(null)
  
  // 重置isResponseStopped状态
  conversationStore.setResponseStopped(false)
  
  // 清空当前消息和会话状态
  conversationStore.resetChat()
  
  // 清空建议问题
  setSuggestedQuestions([])
  
  // 清空文件
  clearFiles()
  clearFilesN()

  // 清空输入框
  inputText.value = ''
  
  // 重置自动滚动状态
  shouldAutoScroll.value = true
  userScrolled.value = false
  
  // 更新URL，移除会话ID
  router.push('/qa')
}

// 添加处理文档预览的函数
const handleDocumentPreview = async (docUrl, highlightText) => {
  try {
    // 显示加载中提示
    ElMessage.info({
      message: '正在获取文档...',
      duration: 1000
    })
    
    console.log('文档URL:', docUrl, '高亮文本:', highlightText)
    
    // 调用API获取实际文档URL并在新窗口中预览
    await difyApi.previewDocumentWithLocation(docUrl, highlightText)
    
  } catch (error) {
    console.error('文档预览失败:', error)
    ElMessage.error('文档预览失败，请重试')
  }
}
const fileDownload=(blob,filename)=>{
    // 创建临时下载链接
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    
    // 设置下载属性（重点在这里）
    a.href = url;
    a.download = filename;
    a.style.display = 'none';
    
    document.body.appendChild(a);
    a.click();
    
    // 清理资源
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
}
// 添加导出为Word文档的函数
const exportToWord = async (message,index) => {
  try {    
    let userQuestion = ''
      const possibleUserMessage = messages.value[index-1]
      // 确认是否为用户消息
      if (possibleUserMessage.role === 'user') {
        userQuestion = possibleUserMessage.content
      }
    // 如果没有找到用户问题，使用默认标题
    const title = userQuestion || '问答导出文档'    
    // 获取要导出的markdown内容，去除HTML标签
    // const contentWithoutHtml = message.content.replace(/<[^>]*>/g, '')
    const contentWithoutHtml = message.content    
    // 准备请求数据
    const requestData = {
      markdown: contentWithoutHtml,
      title: title
    }
    const response = await difyApi.downloadFile(requestData) 
    // 直接获取blob数据
    const blob = new Blob([response], { type: response.type });
    // 获取文件名
    fileDownload(blob,`${requestData.title}.docx`);    
    ElMessage.success('文档导出成功')
  } catch (error) {
    console.error('导出为Word文档失败:', error)
    ElMessage.error('导出为Word文档失败，请重试')
  }
}

// 添加打开文件的函数
const openFile = (file) => {
  if (file && file.url) {           
    window.open(file.url, '_blank');
  } else {
    // ElMessage.error('无法打开文件，链接无效');
  }
}

// 添加搜索相关状态
const isSearchFocused = ref(false)
const searchQuery = ref('')
const searchInputRef = ref(null)

const handleSearchIconClick = () => {
  if (!isSearchFocused.value) {
    isSearchFocused.value = true
    nextTick(() => {
      searchInputRef.value && searchInputRef.value.focus()
    })
  }
}

const onSearchBlur = () => {
  // 只有当搜索框没有内容时才恢复新建按钮显示
  if (!searchQuery.value) {
    setTimeout(() => {
      isSearchFocused.value = false
    }, 100)
  }
}

const handleSearch = () => {
  // 触发搜索事件，通知父组件或调用搜索API
  conversationStore.searchConversations(searchQuery.value)
}

// 监听搜索输入
watch(searchQuery, (val) => {
  handleSearch()
})

const clearSearch = () => {
  searchQuery.value = ''
  nextTick(() => {
    searchInputRef.value && searchInputRef.value.focus()
  })
}
</script>

<style>
@import '../assets/styles/qa-page.css';
.thinking {
  color:gray;
  background-color: #f8f8f8;
  padding: 8px;
  border-radius: 4px;
}
/* 确保统计信息组件位置正确 */
.statistics-section {
  margin: 15px 0;
  width: 100%;
}
.message-files {
  display: flex;
  align-items: center;
  gap: 5px;
}
.message-files-item {
  display: flex;
  align-items: center;
  /* gap: 5px; */
}
/* 额外样式来匹配设计稿 */
.welcome-message {
  /* margin-bottom: 20px; */
  width: 100%;
}

.welcome-message .message-item {
  max-width: 100%;
  position: relative;
  padding-left: 60px;
}

.welcome-message .message-text {
  font-size: 15px;
  font-weight: 500;
}
.guide-special-note {
    padding: 0 0 0 70px;
    /* background-color: #e6a23c; */
    border-radius: 6px;
    color: #878686;
    font-size: 14px;
    line-height: 1.5;
  }
.recommendation-cards-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 8px;
  padding-left:70px;
  overflow: hidden;
  width: 100%;
}

.recommendation-card {
  position: relative;
}

.recommendation-card:hover::after {
  color: #40a9ff;
}

.citation-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.citation-content {
  flex: 1;
  cursor: pointer;
}

.document-preview {
  cursor: pointer;
  margin-left: 10px;
  padding: 3px;
  color: #0085ff;
}

.document-preview:hover {
  color: #e54545;
}

.document-preview-container {
  height: 70vh;
  width: 100%;
}
.message-assistant .message-content {
  margin-right: 0px;
  padding-bottom: 12px;
  margin-left: 10px;
  padding-right:15px;
}
.message-user .message-content {
  margin-left: 70px;
}

.document-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.document-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #f5f5f5;
  color: #999;
}

/* 反馈按钮样式 */
.feedback-buttons {
  display: flex;
  gap: 10px;
  margin-top: 8px;
}

.feedback-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 1px solid #e0e0e0;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s;
}

.feedback-btn:hover {
  background-color: #f5f5f5;
}

.feedback-btn.like:hover, .feedback-btn.like.active {
  color: #52c41a;
  border-color: #52c41a;
}

.feedback-btn.like.active {
  background-color: rgba(82, 196, 26, 0.1);
}

.feedback-btn.dislike:hover, .feedback-btn.dislike.active {
  color: #f5222d;
  border-color: #f5222d;
}

.feedback-btn.dislike.active {
  background-color: rgba(245, 34, 45, 0.1);
}
.feedback-btn.export:hover {
  color: #1890ff;
  border-color: #1890ff;
}

.feedback-btn.export.active {
  background-color: rgba(24, 144, 255, 0.1);
}

/* 建议问题列表样式 */
.suggested-questions {
  margin: 0 0 16px 0;
  padding-left: 70px;
}

.suggested-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
  font-weight: 500;
}

.suggested-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.suggested-question {
  padding: 8px 16px;
  background-color: #f5f5f5;
  border-radius: 16px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid #e0e0e0;
  position: relative;
}

.suggested-question:hover {
  background-color: #e0e0e0;
  color: #40a9ff;
}

.suggested-question:after {
  content: "→";
  position: absolute;
  right: 10px;
  opacity: 0;
  transition: opacity 0.2s;
}

.suggested-question:hover:after {
  opacity: 1;
}

/* 停止生成按钮 */
.stop-btn {
  display: inline-flex;
  align-items: center;
  margin-left: 12px;
  padding: 6px 12px;
  background-color: #f5222d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.stop-btn:hover {
  background-color: #cf1322;
}

.stop-btn .el-icon {
  margin-right: 4px;
}

/* 发送按钮停止状态 */
.stop-btn-small {
  background-color: #f5222d !important;
}

.stop-btn-small:hover {
  background-color: #cf1322 !important;
}

/* 文档预览按钮 */
.document-preview-button {
  margin-top: 15px;
  text-align: center;
}

/* 参考文档样式 */
.reference-block {
  margin: 10px 0;
  padding: 10px;
  background-color: #f0f7ff;
  border-radius: 6px;
  border-left: 3px solid #0085ff;
}

.reference-header {
  font-weight: bold;
  margin-bottom: 5px;
  color: #0085ff;
}

.reference-doc-name {
  font-style: italic;
  margin-bottom: 8px;
  color: #333;
}

.reference-content {
  white-space: wrap;
  color: #555;
  font-size: 14px;
  line-height: 1.5;
}

/* 文档对话框样式 */
.document-dialog .el-dialog__body {
  padding: 0 !important;
  height: 80vh;
  overflow: hidden;
}

.citation-dialog .el-dialog__body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.citation-dialog h3 {
  margin-top: 0;
  color: #0085ff;
  font-size: 18px;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 10px;
}

/* 用户消息反馈按钮样式 */
.user-text {
  position: relative;
}

.user-feedback-buttons {
  position: absolute;
  right: 5px;
  bottom: 5px;
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.user-text:hover .user-feedback-buttons {
  display: flex;
  opacity: 1;
}
:deep(.el-form-item__content) {
  justify-content: center;
}

.user-feedback-buttons .feedback-btn {
  width: 28px;
  height: 28px;
  background-color: rgba(255, 255, 255, 0.8);
}

/* 文件链接样式 */
.file-link {
  color: #1890ff;
  text-decoration: underline;
  cursor: pointer;
  margin-left: 5px;
  transition: color 0.2s;
}

.file-link:hover {
  color: #40a9ff;
}

/* 特殊文档链接样式 */
.doc-preview-link {
  color: #1890ff;
  cursor: pointer;
  text-decoration: none;
  position: relative;
  padding-right: 20px;
}

/* .doc-preview-link:after {
  content: "📄";
  position: absolute;
  right: 0;
  font-size: 14px;
} */

.doc-preview-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}

/* 需求链接样式 */
.requirement-link {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
  color: #fff;
  text-decoration: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
  margin: 4px 0;
}

.requirement-link:hover {
  background: linear-gradient(90deg, #40a9ff 0%, #69c0ff 100%);
  color: #fff;
  text-decoration: none;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
}

.requirement-link:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

/* 添加反馈按钮激活状态的样式 */
.feedback-btn.like.active {
  color: #52c41a;
  border-color: #52c41a;
  background-color: rgba(82, 196, 26, 0.1);
}
/* 添加反馈按钮激活状态的样式 */
.feedback-btn.dislike.active {
  color: #c4421a;
  border-color: #c4421a;
  background-color: rgba(196, 52, 26, 0.1);
}

.header-actions {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
}

.new-chat-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px;
  background-color: #e54545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.new-chat-btn:hover {
  background-color: #c83333;
}

/* 侧边栏样式 */
.sidebar {
  width: 200px;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #f4f3f3;
  box-shadow: 2px 0px 5px 0 rgba(223, 223, 223, 0.1);
  background-color: #fff;
  transition: width 0.3s;
}
.sidebar-header {
  display: flex;
  align-items: center;
  gap: 12px;
  height: 60px;
  padding: 0 16px;
  background: #fff;
}

.new-chat-btn-gradient {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 32px;
  padding: 0 15px;
  border: none;
  border-radius: 16px;
  background: linear-gradient(90deg, #4a90e5 0%, #57a0f9 100%);
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: box-shadow 0.2s;
  box-shadow: 0 2px 8px rgba(90, 111, 251, 0.08);
  white-space: nowrap;
  overflow: hidden;
}

.new-chat-btn-gradient:hover {
  box-shadow: 0 4px 16px rgba(90, 111, 251, 0.16);
}

.search-icon-btn {
  width: 40px;
  height: 40px;
  border: 1px solid #e0e3f7;
  background: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-size: 20px;
  cursor: pointer;
  transition: border-color 0.2s;
}

.search-icon-btn:focus,
.search-icon-btn:hover {
  border-color: #6e6cfb;
}

.search-input-gradient {
  position: relative;
  display: flex;
  align-items: center;
  height: 32px;
  border: 1px solid #e8eaf2;
  background: #fff;
  padding: 0px;
  overflow: hidden;
  border-radius: 16px;
  min-width: 0;
  width: 100%;
  transition: border-color 0.2s;
}
.search-input-gradient:focus-within,.search-input-gradient:hover {
  border-color: #409EFF;
}

.search-input-icon {
  color: #333;
  font-size: 18px;
  margin:0 6px;
}

.search-input-real {
  border: none;
  outline: none;
  font-size: 16px;
  flex: 1;
  background: transparent;
  color: #333;
  min-width: 0;
  transition: width 0.2s, opacity 0.2s, padding 0.2s;
  width: 0;
  padding: 0;
  opacity: 0;
}
.search-flex-wrap.search-focus .search-input-real {
  width: 100%;
  opacity: 1;
  /* padding-left: 8px; */
  transition: width 0.2s, opacity 0.2s, padding 0.2s;
}
.search-clear-icon {
  position: absolute;
  right: 12px;
  color: #bbb;
  font-size: 18px;
  cursor: pointer;
  transition: color 0.2s;
}
.search-clear-icon:hover {
  color: #6e6cfb;
}

.slide-move {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 0.28s cubic-bezier(.4,0,.2,1), transform 0.28s cubic-bezier(.4,0,.2,1);
  will-change: opacity, transform;
}
.slide-move.inactive {
  opacity: 0;
  transform: translateX(-40px);
  pointer-events: none;
}
.slide-move.active {
  opacity: 1;
  transform: translateX(0);
  pointer-events: auto;
}

.sidebar-content {
  flex: 1;
  overflow: hidden;
}

.search-container {
  border-bottom: 1px solid #e8e8e8;
}
/* 主要内容区域样式 */
.main-content {
  flex: 1;
  display: flex;
  min-width: 0;
  height: 100%;
  /* transition: margin-left 0.3s; */
}
.main-content.expanded {
  flex-direction: column;
}

/* 滚动到底部按钮 */
.scroll-to-bottom {
    position: fixed;
    right: calc((100vw - 318px) / 2);
    bottom: 148px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #fff;
    color: #333;font-weight: bold;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    z-index: 20;
    transition: all 0.3s;
    animation: fadeIn 0.3s;
  }

  .scroll-to-bottom:hover {
    background-color: #409EFF;
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

.search-flex-wrap {
  display: flex;
  align-items: center;
  position: relative;
  height: 50px;
  padding: 5px 16px 0;
  background: #fff;
  row-gap: 0px;
  column-gap: 6px;
}

.flex-anim-btn,
.flex-anim-search {
  transition:
    flex-basis 0.36s cubic-bezier(.4,0,.2,1),
    opacity 0.32s cubic-bezier(.4,0,.2,1),
    transform 0.36s cubic-bezier(.4,0,.2,1);
  will-change: flex-basis, opacity, transform;
  min-width: 0;
}

.flex-anim-btn {
  flex-basis: calc(100% - 32px); /* 48px = icon宽度+gap */
  opacity: 1;
  transform: translateX(0);
  min-width: 0;
  max-width: 100%;
  overflow: hidden;
  padding:0;
  text-align: center;
  white-space: nowrap;
}
.flex-anim-btn .el-icon {
  margin-left:20px;
}
.search-flex-wrap .flex-anim-search {
  flex-basis: 36px; /* 只显示icon宽度 */
  opacity: 1;
  transform: translateX(0);
  min-width: 0;
  max-width: 100%;
  overflow: hidden;
}

.search-flex-wrap.search-focus .flex-anim-btn {
  flex-basis: 0 !important;
  opacity: 0;
  transform: translateX(-36px);
  margin-right: 0;
  pointer-events: none;
  width: 0;
}
.search-flex-wrap.search-focus .flex-anim-search {
  flex-basis: 100% !important;
  opacity: 1;
  transform: translateX(-3px);
  pointer-events: auto;
  width: 100%;
}

.search-input-gradient {
  position: relative;
  display: flex;
  align-items: center;
  height: 32px;
  border: 1px solid #e8eaf2;
  background: #fff;
  padding: 0px;
  overflow: hidden;
  border-radius: 16px;
  min-width: 0;
  width: 100%;
  transition: border-color 0.2s;
}
.search-input-gradient:focus-within,.search-input-gradient:hover {
  border-color: #409EFF;
}

.search-input-icon {
  color: #333;
  font-size: 18px;
  margin:0 6px;
}

.search-input-real {
  border: none;
  outline: none;
  font-size: 16px;
  flex: 1;
  background: transparent;
  color: #333;
  min-width: 0;
  transition: width 0.2s, opacity 0.2s, padding 0.2s;
  width: 0;
  padding: 0;
  opacity: 0;
}
.search-flex-wrap.search-focus .search-input-real {
  width: 100%;
  opacity: 1;
  /* padding-left: 8px; */
  transition: width 0.2s, opacity 0.2s, padding 0.2s;
}
</style> 